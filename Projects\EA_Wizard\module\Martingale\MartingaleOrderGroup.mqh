#property strict

#include "../../module/mql4-lib-master/Trade/OrderGroup.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組 - 管理馬丁格爾策略的訂單                           |
//+------------------------------------------------------------------+
class MartingaleOrderGroup : public OrderGroup
{
private:
   // 馬丁格爾策略參數
   int               m_currentLevel;       // 當前層級
   int               m_maxLevel;           // 最大層級數
   double            m_initialLots;        // 初始手數
   double            m_multiplier;         // 手數乘數
   double            m_gridStep;           // 網格間距(點)
   bool              m_isReversed;         // 是否為反向馬丁
   int               m_magicNumber;        // 魔術數字
   string            m_comment;            // 訂單註釋前綴
   double            m_totalProfit;        // 總盈虧
   double            m_breakEvenPrice;     // 盈虧平衡價格
   
public:
   // 構造函數
   MartingaleOrderGroup(string symbol, 
                        double initialLots = 0.01, 
                        double multiplier = 2.0,
                        double gridStep = 20.0,
                        int maxLevel = 5,
                        bool isReversed = false,
                        int magicNumber = 0) 
      : OrderGroup(symbol),
        m_currentLevel(0),
        m_maxLevel(maxLevel),
        m_initialLots(initialLots),
        m_multiplier(multiplier),
        m_gridStep(gridStep),
        m_isReversed(isReversed),
        m_magicNumber(magicNumber),
        m_comment("Martin"),
        m_totalProfit(0.0),
        m_breakEvenPrice(0.0) {}
   
   // 添加新訂單到馬丁序列
   bool              addLevel(int orderType);
   
   // 計算下一層級的手數
   double            calculateNextLots();
   
   // 計算下一層級的價格
   double            calculateNextPrice(int orderType);
   
   // 計算盈虧平衡點
   double            calculateBreakEven();
   
   // 更新馬丁組狀態
   void              updateStatus();
   
   // 檢查是否可以添加新層級
   bool              canAddLevel() { return m_currentLevel < m_maxLevel; }
   
   // 設置組合止盈止損
   void              setGroupTP(double profit);
   void              setGroupSL(double loss);
   
   // 關閉所有馬丁訂單
   bool              closeAll();
   
   // 獲取當前層級
   int               getCurrentLevel() const { return m_currentLevel; }
   
   // 獲取盈虧平衡價格
   double            getBreakEvenPrice() const { return m_breakEvenPrice; }
   
   // 獲取總盈虧
   double            getTotalProfit() { updateStatus(); return m_totalProfit; }
};