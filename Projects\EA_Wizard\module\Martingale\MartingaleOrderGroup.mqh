#property strict

#include "../../module/mql4-lib-master/Trade/OrderGroup.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組類型枚舉                                           |
//+------------------------------------------------------------------+
enum ENUM_MARTINGALE_GROUP_TYPE
{
   MARTINGALE_TYPE_STANDARD,     // 標準馬丁格爾
   MARTINGALE_TYPE_REVERSE,      // 反向馬丁格爾
   MARTINGALE_TYPE_GRID,         // 網格馬丁格爾
   MARTINGALE_TYPE_PROGRESSIVE,  // 漸進式馬丁格爾
   MARTINGALE_TYPE_CUSTOM        // 自定義馬丁格爾
};

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組 - 管理馬丁格爾策略的訂單                           |
//+------------------------------------------------------------------+
class MartingaleOrderGroup : public OrderGroup
{
private:
   // 訂單組識別信息
   int               m_groupId;            // 訂單組獨立ID
   ENUM_MARTINGALE_GROUP_TYPE m_groupType; // 訂單組類型

   // 馬丁格爾策略參數
   int               m_currentLevel;       // 當前層級
   int               m_maxLevel;           // 最大層級數
   double            m_initialLots;        // 初始手數
   double            m_multiplier;         // 手數乘數
   double            m_gridStep;           // 網格間距(點)
   bool              m_isReversed;         // 是否為反向馬丁
   int               m_magicNumber;        // 魔術數字
   string            m_comment;            // 訂單註釋前綴
   double            m_totalProfit;        // 總盈虧
   double            m_breakEvenPrice;     // 盈虧平衡價格

   // ID生成相關
   static int        s_nextGroupId;        // 靜態ID計數器

public:
   // 構造函數
   MartingaleOrderGroup(string symbol,
                        ENUM_MARTINGALE_GROUP_TYPE groupType = MARTINGALE_TYPE_STANDARD,
                        double initialLots = 0.01,
                        double multiplier = 2.0,
                        double gridStep = 20.0,
                        int maxLevel = 5,
                        bool isReversed = false,
                        int magicNumber = 0)
      : OrderGroup(symbol),
        m_groupId(GenerateGroupId()),
        m_groupType(groupType),
        m_currentLevel(0),
        m_maxLevel(maxLevel),
        m_initialLots(initialLots),
        m_multiplier(multiplier),
        m_gridStep(gridStep),
        m_isReversed(isReversed),
        m_magicNumber(magicNumber),
        m_comment("Martin"),
        m_totalProfit(0.0),
        m_breakEvenPrice(0.0) {}

   // 添加新訂單到馬丁序列
   bool              addLevel(int orderType);

   // 計算下一層級的手數
   double            calculateNextLots();

   // 計算下一層級的價格
   double            calculateNextPrice(int orderType);

   // 計算盈虧平衡點
   double            calculateBreakEven();

   // 更新馬丁組狀態
   void              updateStatus();

   // 檢查是否可以添加新層級
   bool              canAddLevel() { return m_currentLevel < m_maxLevel; }

   // 設置組合止盈止損
   void              setGroupTP(double profit);
   void              setGroupSL(double loss);

   // 關閉所有馬丁訂單
   bool              closeAll();

   // 獲取當前層級
   int               getCurrentLevel() const { return m_currentLevel; }

   // 獲取盈虧平衡價格
   double            getBreakEvenPrice() const { return m_breakEvenPrice; }

   // 獲取總盈虧
   double            getTotalProfit() { updateStatus(); return m_totalProfit; }

   // 獲取訂單組ID
   int               getGroupId() const { return m_groupId; }

   // 獲取訂單組類型
   ENUM_MARTINGALE_GROUP_TYPE getGroupType() const { return m_groupType; }

   // 設置訂單組類型
   void              setGroupType(ENUM_MARTINGALE_GROUP_TYPE groupType) { m_groupType = groupType; }

   // 獲取訂單組類型字符串描述
   string            getGroupTypeString() const;

private:
   // 生成唯一的訂單組ID
   static int        GenerateGroupId();

   // 初始化靜態ID計數器
   static void       InitializeIdCounter();
};

// 靜態成員初始化
static int MartingaleOrderGroup::s_nextGroupId = 10000;

//+------------------------------------------------------------------+
//| 實現方法                                                         |
//+------------------------------------------------------------------+

// 生成唯一的訂單組ID
static int MartingaleOrderGroup::GenerateGroupId()
{
   return ++s_nextGroupId;
}

// 初始化靜態ID計數器
static void MartingaleOrderGroup::InitializeIdCounter()
{
   s_nextGroupId = 10000 + (int)(TimeCurrent() % 1000);
}

// 獲取訂單組類型字符串描述
string MartingaleOrderGroup::getGroupTypeString() const
{
   switch(m_groupType)
   {
      case MARTINGALE_TYPE_STANDARD:
         return "標準馬丁格爾";
      case MARTINGALE_TYPE_REVERSE:
         return "反向馬丁格爾";
      case MARTINGALE_TYPE_GRID:
         return "網格馬丁格爾";
      case MARTINGALE_TYPE_PROGRESSIVE:
         return "漸進式馬丁格爾";
      case MARTINGALE_TYPE_CUSTOM:
         return "自定義馬丁格爾";
      default:
         return "未知類型";
   }
}

//+------------------------------------------------------------------+
//| 添加新訂單到馬丁序列                                             |
//+------------------------------------------------------------------+
bool MartingaleOrderGroup::addLevel(int orderType)
{
   // 檢查是否可以添加新層級
   if(!canAddLevel())
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 已達到最大層級: ", m_maxLevel);
      return false;
   }

   // 計算下一層級的手數和價格
   double nextLots = calculateNextLots();
   double nextPrice = calculateNextPrice(orderType);

   if(nextLots <= 0 || nextPrice <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 計算下一層級參數失敗");
      return false;
   }

   // 構建訂單註釋
   string comment = StringFormat("%s_L%d_ID%d", m_comment, m_currentLevel + 1, m_groupId);

   // 發送訂單
   int ticket = OrderSend(
      Symbol(), // 使用當前圖表品種
      orderType,
      nextLots,
      nextPrice,
      3, // 滑點
      0, // 止損 (稍後設置)
      0, // 止盈 (稍後設置)
      comment,
      m_magicNumber,
      0,
      orderType == OP_BUY ? clrBlue : clrRed
   );

   if(ticket < 0)
   {
      int error = GetLastError();
      Print("馬丁格爾訂單組[", m_groupId, "] 添加層級失敗 - 錯誤: ", error);
      return false;
   }

   // 添加到訂單組
   add(ticket);
   m_currentLevel++;

   Print("馬丁格爾訂單組[", m_groupId, "] 成功添加層級 ", m_currentLevel, " - 票號: ", ticket);

   // 更新狀態
   updateStatus();

   return true;
}

//+------------------------------------------------------------------+
//| 計算下一層級的手數                                               |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::calculateNextLots()
{
   if(m_currentLevel == 0)
   {
      return m_initialLots;
   }

   double currentLots = m_initialLots;

   // 根據訂單組類型計算手數
   switch(m_groupType)
   {
      case MARTINGALE_TYPE_STANDARD:
      case MARTINGALE_TYPE_GRID:
         // 標準馬丁格爾：每層級乘以倍數
         for(int i = 0; i < m_currentLevel; i++)
         {
            currentLots *= m_multiplier;
         }
         break;

      case MARTINGALE_TYPE_REVERSE:
         // 反向馬丁格爾：手數遞減
         currentLots = m_initialLots / MathPow(m_multiplier, m_currentLevel);
         break;

      case MARTINGALE_TYPE_PROGRESSIVE:
         // 漸進式馬丁格爾：線性增加
         currentLots = m_initialLots * (1 + m_currentLevel * (m_multiplier - 1));
         break;

      case MARTINGALE_TYPE_CUSTOM:
         // 自定義：使用標準計算作為默認
         for(int i = 0; i < m_currentLevel; i++)
         {
            currentLots *= m_multiplier;
         }
         break;
   }

   // 確保手數在合理範圍內
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);

   if(currentLots < minLot) currentLots = minLot;
   if(currentLots > maxLot) currentLots = maxLot;

   return NormalizeDouble(currentLots, 2);
}

//+------------------------------------------------------------------+
//| 計算下一層級的價格                                               |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::calculateNextPrice(int orderType)
{
   double currentPrice;
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);

   if(orderType == OP_BUY)
   {
      currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

      if(m_isReversed)
      {
         // 反向馬丁：價格向上
         currentPrice += m_gridStep * point;
      }
      else
      {
         // 標準馬丁：價格向下
         currentPrice -= m_gridStep * point;
      }
   }
   else if(orderType == OP_SELL)
   {
      currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);

      if(m_isReversed)
      {
         // 反向馬丁：價格向下
         currentPrice -= m_gridStep * point;
      }
      else
      {
         // 標準馬丁：價格向上
         currentPrice += m_gridStep * point;
      }
   }
   else
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 不支持的訂單類型: ", orderType);
      return 0;
   }

   return NormalizeDouble(currentPrice, digits);
}

//+------------------------------------------------------------------+
//| 計算盈虧平衡點                                                   |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::calculateBreakEven()
{
   if(size() == 0)
   {
      return 0.0;
   }

   double totalLots = 0.0;
   double weightedPrice = 0.0;
   int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);

   // 遍歷所有訂單計算加權平均價格
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 只計算未平倉訂單
         {
            double lots = OrderLots();
            double openPrice = OrderOpenPrice();

            totalLots += lots;
            weightedPrice += lots * openPrice;
         }
      }
   }

   if(totalLots > 0)
   {
      m_breakEvenPrice = NormalizeDouble(weightedPrice / totalLots, digits);
   }
   else
   {
      m_breakEvenPrice = 0.0;
   }

   return m_breakEvenPrice;
}

//+------------------------------------------------------------------+
//| 更新馬丁組狀態                                                   |
//+------------------------------------------------------------------+
void MartingaleOrderGroup::updateStatus()
{
   m_totalProfit = 0.0;
   int openOrders = 0;

   // 清理已平倉的訂單
   clearClosed();

   // 計算總盈虧
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 未平倉訂單
         {
            m_totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            openOrders++;
         }
      }
   }

   // 更新當前層級
   m_currentLevel = openOrders;

   // 重新計算盈虧平衡點
   calculateBreakEven();
}

//+------------------------------------------------------------------+
//| 設置組合止盈                                                     |
//+------------------------------------------------------------------+
void MartingaleOrderGroup::setGroupTP(double profit)
{
   if(profit <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 止盈金額必須大於0");
      return;
   }

   // 計算止盈價格
   double breakEven = calculateBreakEven();
   if(breakEven <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 無法計算盈虧平衡點");
      return;
   }

   double totalLots = groupLots();
   if(totalLots <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 總手數為0");
      return;
   }

   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

   // 計算需要的點數
   double pointsNeeded = profit / (totalLots * tickValue);

   // 設置止盈價格
   groupTakeProfit(breakEven + pointsNeeded * point);

   Print("馬丁格爾訂單組[", m_groupId, "] 設置組合止盈: ", profit, " 於價格: ", breakEven + pointsNeeded * point);
}

//+------------------------------------------------------------------+
//| 設置組合止損                                                     |
//+------------------------------------------------------------------+
void MartingaleOrderGroup::setGroupSL(double loss)
{
   if(loss >= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 止損金額必須小於0");
      return;
   }

   // 計算止損價格
   double breakEven = calculateBreakEven();
   if(breakEven <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 無法計算盈虧平衡點");
      return;
   }

   double totalLots = groupLots();
   if(totalLots <= 0)
   {
      Print("馬丁格爾訂單組[", m_groupId, "] 總手數為0");
      return;
   }

   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

   // 計算需要的點數
   double pointsNeeded = loss / (totalLots * tickValue);

   // 設置止損價格
   groupStopLoss(breakEven + pointsNeeded * point);

   Print("馬丁格爾訂單組[", m_groupId, "] 設置組合止損: ", loss, " 於價格: ", breakEven + pointsNeeded * point);
}

//+------------------------------------------------------------------+
//| 關閉所有馬丁訂單                                                 |
//+------------------------------------------------------------------+
bool MartingaleOrderGroup::closeAll()
{
   bool allClosed = true;
   int closedCount = 0;

   Print("馬丁格爾訂單組[", m_groupId, "] 開始關閉所有訂單...");

   // 遍歷所有訂單並關閉
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 未平倉訂單
         {
            double closePrice;
            color closeColor;

            if(OrderType() == OP_BUY)
            {
               closePrice = SymbolInfoDouble(OrderSymbol(), SYMBOL_BID);
               closeColor = clrRed;
            }
            else if(OrderType() == OP_SELL)
            {
               closePrice = SymbolInfoDouble(OrderSymbol(), SYMBOL_ASK);
               closeColor = clrBlue;
            }
            else
            {
               // 掛單直接刪除
               if(OrderDelete(ticket))
               {
                  closedCount++;
                  Print("馬丁格爾訂單組[", m_groupId, "] 刪除掛單: ", ticket);
               }
               else
               {
                  Print("馬丁格爾訂單組[", m_groupId, "] 刪除掛單失敗: ", ticket, " 錯誤: ", GetLastError());
                  allClosed = false;
               }
               continue;
            }

            // 關閉市價單
            if(OrderClose(ticket, OrderLots(), closePrice, 3, closeColor))
            {
               closedCount++;
               Print("馬丁格爾訂單組[", m_groupId, "] 關閉訂單: ", ticket);
            }
            else
            {
               Print("馬丁格爾訂單組[", m_groupId, "] 關閉訂單失敗: ", ticket, " 錯誤: ", GetLastError());
               allClosed = false;
            }
         }
      }
   }

   // 更新狀態
   updateStatus();

   Print("馬丁格爾訂單組[", m_groupId, "] 關閉完成 - 成功關閉: ", closedCount, " 個訂單");

   return allClosed;
}