#property strict

#include "../../module/mql4-lib-master/Trade/OrderGroup.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組類型枚舉                                           |
//+------------------------------------------------------------------+
enum ENUM_MARTINGALE_GROUP_TYPE
{
   MARTINGALE_TYPE_STANDARD,     // 標準馬丁格爾
   MARTINGALE_TYPE_REVERSE,      // 反向馬丁格爾
   MARTINGALE_TYPE_GRID,         // 網格馬丁格爾
   MARTINGALE_TYPE_PROGRESSIVE,  // 漸進式馬丁格爾
   MARTINGALE_TYPE_CUSTOM        // 自定義馬丁格爾
};

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組 - 管理馬丁格爾策略的訂單                           |
//+------------------------------------------------------------------+
class MartingaleOrderGroup : public OrderGroup
{
private:
   // 訂單組識別信息
   int               m_groupId;            // 訂單組獨立ID
   ENUM_MARTINGALE_GROUP_TYPE m_groupType; // 訂單組類型

   // 馬丁格爾策略參數
   int               m_currentLevel;       // 當前層級
   int               m_maxLevel;           // 最大層級數
   double            m_initialLots;        // 初始手數
   double            m_multiplier;         // 手數乘數
   double            m_gridStep;           // 網格間距(點)
   bool              m_isReversed;         // 是否為反向馬丁
   int               m_magicNumber;        // 魔術數字
   string            m_comment;            // 訂單註釋前綴
   double            m_totalProfit;        // 總盈虧
   double            m_breakEvenPrice;     // 盈虧平衡價格

   // ID生成相關
   static int        s_nextGroupId;        // 靜態ID計數器

public:
   // 構造函數
   MartingaleOrderGroup(string symbol,
                        ENUM_MARTINGALE_GROUP_TYPE groupType = MARTINGALE_TYPE_STANDARD,
                        double initialLots = 0.01,
                        double multiplier = 2.0,
                        double gridStep = 20.0,
                        int maxLevel = 5,
                        bool isReversed = false,
                        int magicNumber = 0)
      : OrderGroup(symbol),
        m_groupId(GenerateGroupId()),
        m_groupType(groupType),
        m_currentLevel(0),
        m_maxLevel(maxLevel),
        m_initialLots(initialLots),
        m_multiplier(multiplier),
        m_gridStep(gridStep),
        m_isReversed(isReversed),
        m_magicNumber(magicNumber),
        m_comment("Martin"),
        m_totalProfit(0.0),
        m_breakEvenPrice(0.0) {}

   // 添加新訂單到馬丁序列
   bool              addLevel(int orderType);

   // 計算下一層級的手數
   double            calculateNextLots();

   // 計算下一層級的價格
   double            calculateNextPrice(int orderType);

   // 計算盈虧平衡點
   double            calculateBreakEven();

   // 更新馬丁組狀態
   void              updateStatus();

   // 檢查是否可以添加新層級
   bool              canAddLevel() { return m_currentLevel < m_maxLevel; }

   // 設置組合止盈止損
   void              setGroupTP(double profit);
   void              setGroupSL(double loss);

   // 關閉所有馬丁訂單
   bool              closeAll();

   // 獲取當前層級
   int               getCurrentLevel() const { return m_currentLevel; }

   // 獲取盈虧平衡價格
   double            getBreakEvenPrice() const { return m_breakEvenPrice; }

   // 獲取總盈虧
   double            getTotalProfit() { updateStatus(); return m_totalProfit; }

   // 獲取訂單組ID
   int               getGroupId() const { return m_groupId; }

   // 獲取訂單組類型
   ENUM_MARTINGALE_GROUP_TYPE getGroupType() const { return m_groupType; }

   // 設置訂單組類型
   void              setGroupType(ENUM_MARTINGALE_GROUP_TYPE groupType) { m_groupType = groupType; }

   // 獲取訂單組類型字符串描述
   string            getGroupTypeString() const;

private:
   // 生成唯一的訂單組ID
   static int        GenerateGroupId();

   // 初始化靜態ID計數器
   static void       InitializeIdCounter();
};

// 靜態成員初始化
static int MartingaleOrderGroup::s_nextGroupId = 10000;

//+------------------------------------------------------------------+
//| 實現方法                                                         |
//+------------------------------------------------------------------+

// 生成唯一的訂單組ID
static int MartingaleOrderGroup::GenerateGroupId()
{
   return ++s_nextGroupId;
}

// 初始化靜態ID計數器
static void MartingaleOrderGroup::InitializeIdCounter()
{
   s_nextGroupId = 10000 + (int)(TimeCurrent() % 1000);
}

// 獲取訂單組類型字符串描述
string MartingaleOrderGroup::getGroupTypeString() const
{
   switch(m_groupType)
   {
      case MARTINGALE_TYPE_STANDARD:
         return "標準馬丁格爾";
      case MARTINGALE_TYPE_REVERSE:
         return "反向馬丁格爾";
      case MARTINGALE_TYPE_GRID:
         return "網格馬丁格爾";
      case MARTINGALE_TYPE_PROGRESSIVE:
         return "漸進式馬丁格爾";
      case MARTINGALE_TYPE_CUSTOM:
         return "自定義馬丁格爾";
      default:
         return "未知類型";
   }
}